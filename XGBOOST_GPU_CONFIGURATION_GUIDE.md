# XGBoost GPU Configuration Guide

## Overview

This guide explains how to resolve XGBoost GPU configuration errors and implement proper fallback mechanisms for systems with or without CUDA support.

## Issues Resolved

### 1. **Deprecated Parameter Warnings**
- ❌ **Old**: `gpu_id=0`, `tree_method='gpu_hist'`, `predictor='gpu_predictor'`
- ✅ **New**: `device='cuda'`, `tree_method='hist'`

### 2. **CUDA Compilation Issues**
- Automatic detection of XGBoost CUDA support
- Graceful fallback to CPU when CUDA is not compiled
- Proper error handling for "Must have at least one device" errors

### 3. **Fallback Mechanism**
- Complete removal of GPU-related parameters during CPU fallback
- Multiple fallback levels (GPU → CPU → Minimal configuration)
- Comprehensive error handling

## Quick Start

### 1. Test Your Configuration

```bash
# Test XGBoost GPU configuration
python test_xgboost_gpu_config.py
```

This will show:
- XGBoost version and CUDA support status
- Recommended configuration parameters
- Fallback mechanism testing

### 2. Use in Your Code

The system automatically handles XGBoost configuration:

```python
# In your main script - XGBoost will automatically use optimal configuration
python main.py
```

## Features Implemented

### 1. **Modern Parameter Syntax**

**Before (Deprecated)**:
```python
xgb_params = {
    'tree_method': 'gpu_hist',
    'gpu_id': 0,
    'predictor': 'gpu_predictor'
}
```

**After (Modern)**:
```python
xgb_params = {
    'tree_method': 'hist',
    'device': 'cuda'  # or 'cpu' for fallback
}
```

### 2. **Automatic GPU Detection**

```python
from utils.xgboost_gpu_utils import get_xgboost_gpu_manager

manager = get_xgboost_gpu_manager()
manager.print_configuration_report()
```

Output example:
```
🔍 XGBOOST GPU CONFIGURATION REPORT
============================================================
📊 Version Information:
   • XGBoost version: 3.0.2
   • PyTorch CUDA available: ✅

🚀 GPU Support:
   • XGBoost CUDA compiled: ✅
   • GPU available: ✅

⚙️ Recommended Configuration:
   • tree_method: hist
   • device: cuda

💡 Status:
   ✅ GPU acceleration available and recommended
============================================================
```

### 3. **Safe Model Creation**

```python
from utils.xgboost_gpu_utils import create_optimized_xgboost_model
from xgboost import XGBRegressor

# Automatically handles GPU/CPU configuration
model = create_optimized_xgboost_model(
    XGBRegressor,
    {'n_estimators': 100, 'max_depth': 6}
)
```

### 4. **Comprehensive Fallback Logic**

The system implements multiple fallback levels:

1. **GPU Configuration**: Try modern GPU parameters
2. **CPU Fallback**: Remove all GPU parameters, use CPU
3. **Minimal Configuration**: Use only essential parameters

```python
# Example of automatic fallback handling
try:
    # Try GPU configuration
    model = XGBRegressor(device='cuda', tree_method='hist', n_estimators=100)
except Exception:
    # Automatic fallback to CPU
    model = XGBRegressor(device='cpu', tree_method='hist', n_estimators=100)
```

## Configuration Details

### Environment Variables

No special environment variables needed for XGBoost GPU configuration.

### Model Registry Updates

The `MODEL_REGISTRY` in `ml_core.py` has been updated:

```python
'xgboost': {
    'name': 'XGBoost',
    'model_class': XGBRegressor,
    'use_modern_gpu_config': True,  # Enable new GPU utilities
    'gpu_check': {
        'param': 'device',      # Modern parameter name
        'gpu_value': 'cuda',    # Modern GPU value
        'cpu_value': 'cpu'      # Modern CPU value
    }
}
```

### Parameter Mapping

| Old Parameter | New Parameter | Purpose |
|---------------|---------------|---------|
| `gpu_id=0` | `device='cuda'` | GPU device selection |
| `tree_method='gpu_hist'` | `tree_method='hist'` + `device='cuda'` | GPU acceleration |
| `predictor='gpu_predictor'` | Removed (automatic) | GPU prediction |

## Error Handling

### Common Errors and Solutions

#### 1. "XGBoost is not compiled with CUDA support"

**Solution**: Automatic CPU fallback
```python
# System automatically detects and uses:
params = {
    'tree_method': 'hist',
    'device': 'cpu'
}
```

#### 2. "Must have at least one device"

**Solution**: Complete GPU parameter removal
```python
# All GPU-related parameters are removed:
gpu_params_to_remove = ['gpu_id', 'predictor', 'gpu_platform_id', 'gpu_device_id']
```

#### 3. "Must have at least 1 validation dataset for early stopping"

**Solution**: Early stopping parameter handling
```python
# Early stopping is removed from default configuration
# Add validation data if early stopping is needed:
model.fit(X_train, y_train, eval_set=[(X_val, y_val)])
```

## Usage Examples

### Basic Usage (Automatic)

```python
from ml_core import create_gpu_optimized_model

# Automatically uses optimal XGBoost configuration
model = create_gpu_optimized_model('xgboost', {
    'n_estimators': 100,
    'learning_rate': 0.1,
    'max_depth': 6
})
```

### Manual Configuration

```python
from utils.xgboost_gpu_utils import XGBoostGPUManager
from xgboost import XGBRegressor

manager = XGBoostGPUManager()

# Get optimal parameters for your system
base_params = {'n_estimators': 100, 'max_depth': 6}
optimal_params, is_gpu = manager.get_optimal_config(base_params)

model = XGBRegressor(**optimal_params)
```

### Advanced Usage with Error Handling

```python
from utils.xgboost_gpu_utils import XGBoostGPUManager

manager = XGBoostGPUManager()

# Safe model creation with comprehensive error handling
try:
    model = manager.create_safe_model(
        XGBRegressor,
        {'n_estimators': 100, 'max_depth': 6}
    )
    print("✅ Model created successfully")
except Exception as e:
    print(f"❌ Model creation failed: {e}")
```

## Testing and Validation

### Run Comprehensive Tests

```bash
python test_xgboost_gpu_config.py
```

Expected output:
```
🎯 Overall: 4/4 tests passed
🎉 All tests passed! XGBoost GPU configuration is working correctly.
💡 Your system can now handle XGBoost with proper GPU/CPU fallback.
```

### Test Individual Components

```python
# Test GPU detection
from utils.xgboost_gpu_utils import check_xgboost_gpu_support
gpu_available = check_xgboost_gpu_support()
print(f"GPU Support: {'✅' if gpu_available else '❌'}")

# Test parameter optimization
from utils.xgboost_gpu_utils import get_xgboost_optimal_params
optimal = get_xgboost_optimal_params({'n_estimators': 100})
print(f"Optimal params: {optimal}")
```

## Troubleshooting

### Still Getting GPU Errors?

1. **Check XGBoost Installation**:
   ```bash
   python -c "import xgboost as xgb; print(xgb.__version__)"
   ```

2. **Verify CUDA Support**:
   ```python
   from utils.xgboost_gpu_utils import get_xgboost_gpu_manager
   manager = get_xgboost_gpu_manager()
   print(f"CUDA Support: {manager.cuda_support}")
   ```

3. **Force CPU Mode**:
   ```python
   # Force CPU configuration
   model = XGBRegressor(device='cpu', tree_method='hist')
   ```

### Performance Issues?

1. **Check Configuration**:
   ```python
   manager.print_configuration_report()
   ```

2. **Monitor GPU Usage**:
   ```bash
   nvidia-smi  # Check GPU utilization during training
   ```

### Installation Issues?

If XGBoost doesn't have CUDA support:

```bash
# Install XGBoost with GPU support
pip uninstall xgboost
pip install xgboost[gpu]
# or
conda install -c conda-forge xgboost-gpu
```

## Performance Expectations

### GPU vs CPU Performance

- **GPU Acceleration**: 2-10x faster training (depending on dataset size)
- **Memory Usage**: Similar to CPU, but utilizes GPU memory
- **Compatibility**: Works with XGBoost 1.6+ and CUDA 11.0+

### Recommended Settings

For optimal performance:

```python
optimal_params = {
    'tree_method': 'hist',
    'device': 'cuda',
    'n_estimators': 1000,
    'max_depth': 6,
    'learning_rate': 0.1,
    'subsample': 0.8,
    'colsample_bytree': 0.8
}
```

## Support

The XGBoost GPU configuration system provides:

- ✅ Automatic detection of CUDA support
- ✅ Modern parameter syntax
- ✅ Comprehensive fallback mechanisms
- ✅ Complete error handling
- ✅ Performance optimization

Your XGBoost models will now work reliably on any system, using GPU acceleration when available and falling back gracefully to CPU when needed.
